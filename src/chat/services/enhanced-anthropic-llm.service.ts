import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ILlmService,
  LlmRecommendation,
  CandidateEntity,
  ChatResponse,
  ConversationContext,
  UserIntent,
} from '../../common/llm/interfaces/llm.service.interface';

/**
 * Enhanced Anthropic LLM Service with advanced anti-repetition and context awareness
 * Designed to provide world-class AI tool recommendations with engaging conversations
 */
@Injectable()
export class EnhancedAnthropicLlmService implements ILlmService {
  private readonly logger = new Logger(EnhancedAnthropicLlmService.name);
  private readonly apiKey: string | undefined;
  private readonly apiUrl = 'https://api.anthropic.com/v1/messages';

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('ANTHROPIC_API_KEY');
    if (!this.apiKey) {
      this.logger.warn('ANTHROPIC_API_KEY is not set in environment variables.');
    }
  }

  async getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation> {
    this.logger.log(
      `Getting enhanced Anthropic recommendation for problem: "${problemDescription}" with ${candidateEntities.length} candidates`,
    );

    if (!this.apiKey) {
      this.logger.warn('Anthropic API key not available, using enhanced fallback');
      return this.getEnhancedFallbackRecommendation(candidateEntities);
    }

    try {
      const prompt = this.buildEnhancedRecommendationPrompt(
        problemDescription,
        candidateEntities,
      );

      const response = await this.callAnthropicAPI(prompt);
      const recommendation = this.parseAnthropicResponse(response, candidateEntities);

      this.logger.log(
        `Enhanced Anthropic recommendation generated: ${recommendation.recommendedEntityIds.length} entities recommended`,
      );

      return recommendation;
    } catch (error) {
      this.logger.error('Error generating enhanced Anthropic recommendation', error.stack);
      return this.getEnhancedFallbackRecommendation(candidateEntities);
    }
  }

  async getChatResponse(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse> {
    const startTime = Date.now();

    this.logger.log(
      `Getting enhanced Anthropic chat response for session: ${context.sessionId}, stage: ${context.conversationStage}`,
    );

    if (!this.apiKey) {
      this.logger.warn('Anthropic API key not available, using enhanced fallback');
      return this.getEnhancedFallbackChatResponse(userMessage, context);
    }

    try {
      // Pre-process context for better understanding
      const processedContext = this.preprocessConversationContext(context);
      const repetitionAnalysis = this.analyzeRepetition(userMessage, processedContext);
      
      const intent = await this.classifyIntent(userMessage, processedContext);
      const prompt = this.buildEnhancedChatPrompt(
        userMessage, 
        processedContext, 
        intent, 
        candidateEntities,
        repetitionAnalysis
      );
      
      const response = await this.callAnthropicAPI(prompt);
      const chatResponse = this.parseEnhancedChatResponse(
        response, 
        intent, 
        processedContext, 
        candidateEntities,
        repetitionAnalysis
      );

      // Validate response quality and check for hallucinated tools in message text
      const validatedResponse = this.validateAndEnhanceResponse(
        chatResponse,
        userMessage,
        processedContext,
        repetitionAnalysis
      );

      // 🎯 TEMPORARILY DISABLED: Message content validation (causing repetition issues)
      // const finalValidatedResponse = this.validateMessageContentForHallucination(
      //   validatedResponse,
      //   candidateEntities || []
      // );
      const finalValidatedResponse = validatedResponse;

      finalValidatedResponse.metadata = {
        responseTime: Date.now() - startTime,
        llmProvider: 'ANTHROPIC_ENHANCED',
        tokensUsed: response.length,
      };

      return finalValidatedResponse;
    } catch (error) {
      this.logger.error('Error generating enhanced Anthropic chat response', error.stack);
      return this.getEnhancedFallbackChatResponse(userMessage, context);
    }
  }

  async classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent> {
    if (!this.apiKey) return this.getEnhancedFallbackIntent(userMessage, context);

    try {
      const prompt = this.buildEnhancedIntentClassificationPrompt(userMessage, context);
      const response = await this.callAnthropicAPI(prompt);
      return this.parseIntentResponse(response);
    } catch (error) {
      this.logger.error('Error classifying intent', error.stack);
      return this.getEnhancedFallbackIntent(userMessage, context);
    }
  }

  async generateFollowUpQuestions(context: ConversationContext): Promise<string[]> {
    if (!this.apiKey) return this.getEnhancedFallbackFollowUpQuestions(context);

    try {
      const prompt = this.buildEnhancedFollowUpPrompt(context);
      const response = await this.callAnthropicAPI(prompt);
      return this.parseFollowUpResponse(response);
    } catch (error) {
      this.logger.error('Error generating follow-up questions', error.stack);
      return this.getEnhancedFallbackFollowUpQuestions(context);
    }
  }

  async shouldTransitionToRecommendations(context: ConversationContext): Promise<{ shouldTransition: boolean; reason: string }> {
    if (!this.apiKey) return this.getEnhancedTransitionDecision(context);

    try {
      const prompt = this.buildEnhancedTransitionPrompt(context);
      const response = await this.callAnthropicAPI(prompt);
      return this.parseTransitionResponse(response);
    } catch (error) {
      this.logger.error('Error evaluating transition', error.stack);
      return this.getEnhancedTransitionDecision(context);
    }
  }

  // Enhanced helper methods
  private preprocessConversationContext(context: ConversationContext): ConversationContext {
    // Add conversation analysis and preprocessing
    const processedContext = { ...context };
    
    // Analyze conversation patterns - extend metadata with additional properties
    (processedContext.metadata as any) = {
      ...processedContext.metadata,
      conversationLength: context.messages?.length || 0,
      userMessageCount: context.messages?.filter(m => m.role === 'user').length || 0,
      assistantMessageCount: context.messages?.filter(m => m.role === 'assistant').length || 0,
      lastUserMessage: context.messages?.filter(m => m.role === 'user').pop()?.content || '',
      conversationTopics: this.extractConversationTopics(context),
      userEngagementLevel: this.assessUserEngagement(context),
    };

    return processedContext;
  }

  private analyzeRepetition(userMessage: string, context: ConversationContext): {
    isRepeated: boolean;
    previousOccurrences: number;
    lastOccurrence: string | null;
    similarQuestions: string[];
    repetitionType: 'exact' | 'similar' | 'none';
  } {
    const userMessages = context.messages?.filter(m => m.role === 'user') || [];
    const currentMessageLower = userMessage.toLowerCase().trim();
    
    // Check for exact repetition
    const exactMatches = userMessages.filter(msg => 
      msg.content.toLowerCase().trim() === currentMessageLower
    );
    
    // Check for similar questions (using simple similarity)
    const similarQuestions = userMessages.filter(msg => {
      const msgLower = msg.content.toLowerCase();
      const similarity = this.calculateStringSimilarity(currentMessageLower, msgLower);
      return similarity > 0.7 && similarity < 1.0; // Similar but not exact
    }).map(msg => msg.content);

    return {
      isRepeated: exactMatches.length > 0,
      previousOccurrences: exactMatches.length,
      lastOccurrence: exactMatches.length > 0 ? exactMatches[exactMatches.length - 1].content : null,
      similarQuestions,
      repetitionType: exactMatches.length > 0 ? 'exact' : (similarQuestions.length > 0 ? 'similar' : 'none'),
    };
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    // Simple Jaccard similarity for quick comparison
    const set1 = new Set(str1.toLowerCase().split(/\s+/));
    const set2 = new Set(str2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  private extractConversationTopics(context: ConversationContext): string[] {
    const topics = new Set<string>();
    const messages = context.messages || [];

    const topicKeywords = {
      'work': ['work', 'job', 'business', 'company', 'professional'],
      'education': ['education', 'learning', 'student', 'teaching'],
      'content': ['content', 'writing', 'video', 'image', 'creative'],
      'automation': ['automation', 'workflow', 'process', 'efficiency'],
      'data': ['data', 'analysis', 'analytics', 'insights'],
      'programming': ['code', 'coding', 'programming', 'development'],
    };

    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      Object.entries(topicKeywords).forEach(([topic, keywords]) => {
        if (keywords.some(keyword => content.includes(keyword))) {
          topics.add(topic);
        }
      });
    });

    return Array.from(topics);
  }

  private assessUserEngagement(context: ConversationContext): 'high' | 'medium' | 'low' {
    const messageCount = context.messages?.length || 0;
    const userMessages = context.messages?.filter(m => m.role === 'user') || [];
    const avgMessageLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / Math.max(userMessages.length, 1);

    if (messageCount >= 6 && avgMessageLength > 50) return 'high';
    if (messageCount >= 3 && avgMessageLength > 20) return 'medium';
    return 'low';
  }

  private buildEnhancedChatPrompt(
    userMessage: string,
    context: ConversationContext,
    intent: UserIntent,
    candidateEntities?: CandidateEntity[],
    repetitionAnalysis?: any
  ): string {
    const conversationHistory = context.messages?.slice(-8).map(msg => `${msg.role}: ${msg.content}`).join('\n') || '';
    const entitiesContext = candidateEntities ? this.formatEntitiesForChat(candidateEntities) : '';
    const userProfile = this.formatUserProfile(context);

    // Build repetition context
    let repetitionContext = '';
    if (repetitionAnalysis?.isRepeated) {
      repetitionContext = `
**🚨 CRITICAL: REPETITION DETECTED**
- User has asked this EXACT question ${repetitionAnalysis.previousOccurrences} time(s) before
- Previous identical question: "${repetitionAnalysis.lastOccurrence}"
- You MUST acknowledge this repetition and provide a DIFFERENT perspective or ask for clarification
- DO NOT give the same response as before
- Be helpful but acknowledge the repetition directly`;
    } else if (repetitionAnalysis?.similarQuestions.length > 0) {
      repetitionContext = `
**⚠️ SIMILAR QUESTIONS DETECTED**
- User has asked similar questions before: ${repetitionAnalysis.similarQuestions.slice(0, 2).join(', ')}
- Provide a fresh perspective or build upon previous discussions
- Avoid repeating the same information`;
    }

    return `You are the world's best AI assistant for discovering and recommending AI tools. You are conversational, knowledgeable, and focused on helping users find the perfect AI solutions for their specific needs.

${repetitionContext}

**ABSOLUTE RULES - NEVER VIOLATE THESE:**
1. NEVER say "I apologize, but I'm having trouble accessing our conversation history"
2. NEVER claim you can't access conversation history - you can see it below
3. ALWAYS acknowledge what the user has already told you
4. NEVER ask questions you've already asked in this conversation
5. If user repeats a question, acknowledge it and provide a NEW angle or ask for clarification
6. Build upon previous knowledge rather than starting over
7. Be engaging, helpful, and move the conversation forward
8. 🎯 CRITICAL: ONLY mention AI tools that are in the "Relevant AI Tools Available" list below
9. 🎯 NEVER invent or hallucinate tool names, IDs, or details not in the provided list
10. 🎯 If no relevant tools are provided, focus on understanding user needs better

**Current Conversation Context:**
- Session: ${context.sessionId}
- Stage: ${context.conversationStage}
- Messages: ${context.messages?.length || 0}
- User Intent: ${intent.type} (confidence: ${intent.confidence})
- Topics Discussed: ${(context.metadata as any)?.conversationTopics?.join(', ') || 'None yet'}

**User Profile:**
${userProfile}

**Full Conversation History:**
${conversationHistory}

**Current User Message:**
"${userMessage}"

${entitiesContext ? `**Relevant AI Tools Available:**\n${entitiesContext}` : ''}

**Your Task:**
1. Respond naturally and helpfully to the user's message
2. Use the conversation history to provide context-aware responses
3. If entities are provided, mention relevant ones naturally using their EXACT names and IDs
4. Ask NEW questions that haven't been covered before (if needed)
5. Guide the user toward finding the perfect AI tools for their needs
6. Be encouraging and build rapport

**Response Format (JSON):**
{
  "message": "Your engaging, context-aware response here",
  "discoveredEntities": [{"id": "EXACT-ID-FROM-LIST", "name": "EXACT-NAME-FROM-LIST", "relevanceScore": 0.9, "reason": "Why it's relevant"}],
  "followUpQuestions": ["Only NEW questions that build on the conversation"],
  "suggestedActions": [{"type": "explore_tool", "label": "Learn more about...", "data": {}}],
  "shouldTransitionToRecommendations": false,
  "conversationStage": "${context.conversationStage}"
}

**CRITICAL FOR discoveredEntities:**
- ONLY use entity IDs and names from the "Relevant AI Tools Available" list above
- If no relevant tools are provided, use an empty array: "discoveredEntities": []
- NEVER invent tool names or IDs that aren't in the provided list
- Use EXACT entity IDs and names as shown in the list

Remember: You are building the world's best AI tool recommendation system. Be helpful, engaging, and always move the conversation forward!`;
  }

  private buildEnhancedRecommendationPrompt(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): string {
    const entitiesContext = candidateEntities
      .map((entity, index) => {
        const categories = entity.categories?.map((c) => c.category.name).join(', ') || 'General';
        const tags = entity.tags?.map((t) => t.tag.name).join(', ') || 'None';
        const features = entity.features?.map((f) => f.feature.name).join(', ') || 'Various';

        return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.entityType?.name || 'AI Tool'}
   - Description: ${entity.shortDescription || entity.description || 'AI tool for various tasks'}
   - Categories: ${categories}
   - Tags: ${tags}
   - Features: ${features}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5 (${entity.reviewCount} reviews)` : 'Not yet rated'}
   - Pricing: See website for pricing`;
      })
      .join('\n\n');

    return `You are the world's leading expert in AI tools and technology. Your mission is to provide the most accurate, helpful, and insightful recommendations.

**User's Need:**
"${problemDescription}"

**Available AI Tools:**
${entitiesContext}

**Your Task:**
Analyze the user's specific needs and recommend the TOP 3-5 most suitable AI tools from the list above.

**Evaluation Criteria:**
1. Direct relevance to the user's problem
2. Tool capabilities and features
3. User ratings and reviews
4. Ease of use and learning curve
5. Value for money
6. Integration capabilities
7. Community and support

**Response Format (JSON):**
{
  "recommendedEntityIds": ["entity-id-1", "entity-id-2", "entity-id-3"],
  "explanation": "Based on your need for [specific problem], I recommend these tools: 1) [Tool Name] - [specific reason why it's perfect for their need]... 2) [Tool Name] - [specific reason]... 3) [Tool Name] - [specific reason]... Each recommendation should be tailored to their exact requirements."
}

**Important:** Only include entity IDs from the provided list. Focus on quality over quantity - better to recommend 3 perfect matches than 5 mediocre ones.`;
  }

  private buildEnhancedIntentClassificationPrompt(userMessage: string, context: ConversationContext): string {
    const recentMessages = context.messages?.slice(-3).map(msg => `${msg.role}: ${msg.content}`).join('\n') || '';

    return `Analyze the user's intent from their message and conversation context.

**Conversation Context:**
${recentMessages}

**Current User Message:**
"${userMessage}"

**Intent Categories:**
- discovery: User is exploring what AI tools exist for their needs
- comparison: User wants to compare specific tools or categories  
- specific_tool: User is asking about a particular tool
- general_question: User has general questions about AI tools
- refinement: User is narrowing down their requirements
- recommendation_request: User is ready for specific recommendations

**Response Format (JSON):**
{
  "type": "discovery|comparison|specific_tool|general_question|refinement|recommendation_request",
  "confidence": 0.85,
  "entities": ["mentioned entity names"],
  "categories": ["mentioned categories"],
  "features": ["mentioned features"],
  "constraints": {
    "budget": "free|low|medium|high|not_specified",
    "technical_level": "beginner|intermediate|advanced|not_specified",
    "use_case": "specific use case if mentioned"
  }
}`;
  }

  private buildEnhancedFollowUpPrompt(context: ConversationContext): string {
    const lastMessage = context.messages?.[context.messages.length - 1];
    const previousQuestions = this.extractPreviousQuestions(context);

    return `Generate 2-3 intelligent follow-up questions to help the user discover the right AI tools.

**Conversation Stage:** ${context.conversationStage}
**User's Last Message:** "${lastMessage?.content || 'No previous message'}"
**Discovered Entities:** ${context.discoveredEntities?.length || 0} tools found so far
**Topics Discussed:** ${(context.metadata as any)?.conversationTopics?.join(', ') || 'None'}

**Questions Already Asked:**
${previousQuestions.length > 0 ? previousQuestions.join('\n- ') : 'None yet'}

**Guidelines:**
- Ask questions that help narrow down their specific needs
- Consider their technical level and use case
- Be conversational and helpful
- Focus on practical aspects like budget, features, or use cases
- NEVER repeat questions that have already been asked
- Build upon information already gathered

**Response Format (JSON):**
{
  "questions": ["New question 1?", "New question 2?", "New question 3?"]
}`;
  }

  private buildEnhancedTransitionPrompt(context: ConversationContext): string {
    return `Determine if the conversation is ready to transition to formal recommendations.

**Conversation Analysis:**
- Stage: ${context.conversationStage}
- Messages: ${context.messages?.length || 0}
- Discovered Entities: ${context.discoveredEntities?.length || 0}
- User Preferences: ${JSON.stringify(context.userPreferences)}
- Topics Covered: ${(context.metadata as any)?.conversationTopics?.join(', ') || 'None'}
- Engagement Level: ${(context.metadata as any)?.userEngagementLevel || 'unknown'}

**Transition Criteria:**
- User has clearly expressed their needs
- We have enough information about their requirements
- User seems ready for specific recommendations
- Conversation has progressed beyond initial discovery
- User has engaged meaningfully (not just one-word answers)

**Response Format (JSON):**
{
  "shouldTransition": true/false,
  "reason": "Detailed explanation of the decision"
}`;
  }

  private async callAnthropicAPI(prompt: string): Promise<string> {
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 2000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
    };

    if (this.apiKey) {
      headers['x-api-key'] = this.apiKey;
    }

    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.content && data.content[0]?.text) {
      return data.content[0].text;
    }

    throw new Error('Invalid response format from Anthropic API');
  }

  private parseEnhancedChatResponse(
    response: string,
    intent: UserIntent,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
    repetitionAnalysis?: any
  ): ChatResponse {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);

      // 🎯 RE-ENABLED: Entity validation with proper CandidateEntity objects
      this.logger.debug(`🔍 PARSING DEBUG - Original discoveredEntities: ${JSON.stringify(parsed.discoveredEntities)}`);
      this.logger.debug(`🔍 PARSING DEBUG - Candidate entities count: ${candidateEntities?.length || 0}`);

      const validatedDiscoveredEntities = this.validateDiscoveredEntities(
        parsed.discoveredEntities || [],
        candidateEntities || []
      );

      this.logger.debug(`🔍 PARSING DEBUG - Validated discoveredEntities: ${JSON.stringify(validatedDiscoveredEntities)}`);

      return {
        message: parsed.message || this.getContextAwareFallbackMessage(context, repetitionAnalysis),
        intent,
        discoveredEntities: validatedDiscoveredEntities,
        followUpQuestions: parsed.followUpQuestions || [],
        suggestedActions: parsed.suggestedActions || [],
        shouldTransitionToRecommendations: parsed.shouldTransitionToRecommendations || false,
        conversationStage: parsed.conversationStage || context.conversationStage,
        metadata: { responseTime: 0, llmProvider: 'ANTHROPIC_ENHANCED' },
      };
    } catch (error) {
      this.logger.warn('Failed to parse enhanced chat response, using enhanced fallback', error.message);
      return this.getEnhancedFallbackChatResponse('', context);
    }
  }

  /**
   * 🎯 CRITICAL: Validate discovered entities to prevent hallucination
   * Only allow entities that actually exist in our candidate entities list
   */
  private validateDiscoveredEntities(
    discoveredEntities: any[],
    candidateEntities: CandidateEntity[]
  ): Array<{
    id: string;
    name: string;
    relevanceScore: number;
    reason: string;
  }> {
    // 🎯 ROBUST VALIDATION: Add comprehensive error handling
    try {
      if (!discoveredEntities || !Array.isArray(discoveredEntities)) {
        this.logger.debug('No discovered entities to validate');
        return [];
      }

      if (!candidateEntities || candidateEntities.length === 0) {
        this.logger.warn('No candidate entities provided for validation - this may indicate an issue with entity discovery');
        return [];
      }

      const validEntityIds = new Set(candidateEntities.map(e => e.id));
      const validEntityNames = new Map(candidateEntities.map(e => [e.name.toLowerCase(), e]));

    const validatedEntities: Array<{
      id: string;
      name: string;
      relevanceScore: number;
      reason: string;
    }> = [];

    for (const entity of discoveredEntities) {
      let validEntity: CandidateEntity | null = null;

      // First, try to match by ID (most reliable)
      if (entity.id && validEntityIds.has(entity.id)) {
        validEntity = candidateEntities.find(e => e.id === entity.id) || null;
      }

      // If no ID match, try to match by name (case-insensitive)
      if (!validEntity && entity.name) {
        const nameLower = entity.name.toLowerCase();
        validEntity = validEntityNames.get(nameLower) || null;
      }

      // Only include if we found a valid match in our database
      if (validEntity) {
        validatedEntities.push({
          id: validEntity.id,
          name: validEntity.name, // Use the actual name from our database
          relevanceScore: Math.min(Math.max(entity.relevanceScore || 0.8, 0), 1), // Clamp between 0-1
          reason: entity.reason || `Relevant AI tool for your needs`
        });

        this.logger.debug(`Validated entity: ${validEntity.name} (${validEntity.id})`);
      } else {
        // Log hallucinated entities for monitoring
        this.logger.warn(`🚨 HALLUCINATED ENTITY DETECTED: ${JSON.stringify(entity)} - This entity does not exist in our database!`);
      }
    }

      this.logger.log(`Validated ${validatedEntities.length} out of ${discoveredEntities.length} discovered entities`);
      return validatedEntities;

    } catch (error) {
      this.logger.error('Entity validation failed completely, using fallback', error.message);
      // Fallback: return empty array rather than breaking the chat
      return [];
    }
  }

  /**
   * 🎯 CRITICAL: Validate message content for hallucinated tool mentions
   * Scans the message text for tool names that don't exist in our database
   */
  private validateMessageContentForHallucination(
    response: ChatResponse,
    candidateEntities: CandidateEntity[]
  ): ChatResponse {
    if (!response.message) {
      return response;
    }

    // Create a set of valid tool names from our database (case-insensitive)
    const validToolNames = new Set(
      candidateEntities.map(entity => entity.name.toLowerCase())
    );

    // Specific AI tool patterns that might be hallucinated
    // Focus on known AI tool names rather than generic patterns to avoid false positives
    const suspiciousPatterns = [
      // Specific AI tools that are commonly hallucinated
      /\b(ChatGPT|Claude|GPT-4|GPT-3|Midjourney|DALL-E|DALL·E|Stable Diffusion|Runway ML|Runway|Synthesia|Jasper|Copy\.ai|Writesonic|Grammarly|Notion AI|Canva|Figma|Adobe Firefly|Filmora|DaVinci Resolve|Luma|Pika|Descript|Replicate|Hugging Face|OpenAI|Anthropic|Cohere|AI21|Perplexity|Character\.ai|Bard|Bing Chat|GitHub Copilot|Codex|InstructGPT|PaLM|LaMDA|BERT|T5|RoBERTa)\b/gi,
      // AI tool patterns with specific suffixes
      /\b([A-Z][a-z]{2,}(?:[A-Z][a-z]+)*)\s+(AI|Pro|Plus|Studio|Bot|Assistant|Generator|Creator|Maker|Builder)\b/g,
      // Company + AI patterns
      /\b(Google|Microsoft|Meta|Facebook|Amazon|Apple|IBM|NVIDIA|Intel|Salesforce|Oracle)\s+(AI|Bard|Copilot|Assistant|Bot)\b/gi,
    ];

    let messageText = response.message;
    let foundHallucinations = false;

    // Check for suspicious tool mentions
    for (const pattern of suspiciousPatterns) {
      const matches = messageText.match(pattern);
      if (matches) {
        for (const match of matches) {
          const toolName = match.toLowerCase().trim();

          // Skip common words that aren't tools
          const commonWords = ['ai', 'pro', 'plus', 'studio', 'the', 'and', 'or', 'for', 'with', 'to', 'in', 'on', 'at', 'by'];
          if (commonWords.includes(toolName) || toolName.length < 3) {
            continue;
          }

          // Check if this tool name exists in our database
          if (!validToolNames.has(toolName) && !this.isGenericTerm(toolName)) {
            this.logger.warn(`🚨 HALLUCINATED TOOL IN MESSAGE: "${match}" - This tool is not in our database!`);
            foundHallucinations = true;

            // Replace the hallucinated tool mention with a generic term
            const genericReplacement = this.getGenericReplacement(match);
            messageText = messageText.replace(new RegExp(match, 'gi'), genericReplacement);
          }
        }
      }
    }

    if (foundHallucinations) {
      this.logger.warn(`🎯 MESSAGE CONTENT SANITIZED: Removed hallucinated tool mentions`);

      // Add a disclaimer to the message
      messageText += '\n\n*Note: I can only recommend AI tools that are verified and available in our database.*';

      return {
        ...response,
        message: messageText,
      };
    }

    return response;
  }

  /**
   * Check if a term is generic and not a specific tool name
   */
  private isGenericTerm(term: string): boolean {
    const genericTerms = [
      'ai', 'tool', 'software', 'platform', 'service', 'app', 'application',
      'system', 'solution', 'technology', 'program', 'website', 'online',
      'digital', 'virtual', 'smart', 'intelligent', 'automated', 'machine',
      'learning', 'neural', 'network', 'algorithm', 'model', 'api'
    ];
    return genericTerms.includes(term.toLowerCase());
  }

  /**
   * Get a generic replacement for a hallucinated tool mention
   */
  private getGenericReplacement(toolName: string): string {
    if (toolName.toLowerCase().includes('ai')) {
      return 'AI tools';
    }
    if (toolName.toLowerCase().includes('video') || toolName.toLowerCase().includes('edit')) {
      return 'video editing tools';
    }
    if (toolName.toLowerCase().includes('write') || toolName.toLowerCase().includes('text')) {
      return 'writing tools';
    }
    if (toolName.toLowerCase().includes('image') || toolName.toLowerCase().includes('photo')) {
      return 'image generation tools';
    }
    return 'AI tools';
  }

  private validateAndEnhanceResponse(
    response: ChatResponse,
    userMessage: string,
    context: ConversationContext,
    repetitionAnalysis?: any
  ): ChatResponse {
    // Check for problematic responses
    const problematicPhrases = [
      'i apologize, but i\'m having trouble accessing',
      'i don\'t have access to our conversation history',
      'let\'s start fresh',
      'i can\'t see our previous conversation'
    ];

    const messageLower = response.message.toLowerCase();
    const hasProblematicPhrase = problematicPhrases.some(phrase => messageLower.includes(phrase));

    if (hasProblematicPhrase) {
      this.logger.warn('Detected problematic response, replacing with enhanced fallback');
      response.message = this.getContextAwareFallbackMessage(context, repetitionAnalysis);
    }

    // Ensure follow-up questions are not repetitive
    if (response.followUpQuestions) {
      const previousQuestions = this.extractPreviousQuestions(context);
      response.followUpQuestions = response.followUpQuestions.filter(q => 
        !previousQuestions.some(prev => 
          this.calculateStringSimilarity(q.toLowerCase(), prev.toLowerCase()) > 0.8
        )
      );
    }

    return response;
  }

  private getContextAwareFallbackMessage(context: ConversationContext, repetitionAnalysis?: any): string {
    const messageCount = context.messages?.length || 0;
    const stage = context.conversationStage;
    const topics = (context.metadata as any)?.conversationTopics || [];

    if (repetitionAnalysis?.isRepeated) {
      return `I notice you've asked this question before. Let me approach it from a different angle - could you tell me more about what specific aspect you'd like me to focus on? For example, are you looking for budget-friendly options, enterprise solutions, or tools with specific features?`;
    }

    if (messageCount === 0) {
      return `Hello! I'm here to help you discover the perfect AI tools for your needs. I specialize in understanding exactly what you're trying to accomplish and matching you with the best AI solutions available. What kind of project or challenge are you working on?`;
    }

    if (topics.length > 0) {
      const topicList = topics.slice(0, 2).join(' and ');
      return `Based on our discussion about ${topicList}, I can help you find some excellent AI tools. What specific features or capabilities are most important to you right now?`;
    }

    const stageMessages = {
      greeting: `Great to meet you! I'm excited to help you find the perfect AI tools. What kind of work or projects are you focusing on?`,
      discovery: `I'm getting a good sense of what you need. Let me help you explore some specific options that could work well for your situation.`,
      refinement: `Perfect! Now that I understand your requirements better, I can point you toward some excellent solutions.`,
      recommendation: `Based on everything we've discussed, I have some fantastic AI tools in mind that should be perfect for your needs.`,
      comparison: `Let me help you compare the best options so you can make the right choice for your specific situation.`
    };

    return stageMessages[stage] || stageMessages.discovery;
  }

  private extractPreviousQuestions(context: ConversationContext): string[] {
    const questions: string[] = [];
    const messages = context.messages || [];

    messages.forEach(msg => {
      if (msg.role === 'assistant' && msg.content.includes('?')) {
        const questionMatches = msg.content.match(/[^.!]*\?/g);
        if (questionMatches) {
          questions.push(...questionMatches.map(q => q.trim()));
        }
      }
    });

    return questions;
  }

  private parseAnthropicResponse(
    response: string,
    candidateEntities: CandidateEntity[],
  ): LlmRecommendation {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);
      
      if (!parsed.recommendedEntityIds || !Array.isArray(parsed.recommendedEntityIds)) {
        throw new Error('Invalid response structure');
      }

      const validEntityIds = candidateEntities.map((e) => e.id);
      const filteredIds = parsed.recommendedEntityIds.filter((id: string) =>
        validEntityIds.includes(id),
      );

      return {
        recommendedEntityIds: filteredIds.slice(0, 5),
        explanation: parsed.explanation || 'AI recommendation generated successfully.',
      };
    } catch (error) {
      this.logger.warn('Failed to parse Anthropic response, using enhanced fallback', error.message);
      return this.getEnhancedFallbackRecommendation(candidateEntities);
    }
  }

  private parseIntentResponse(response: string): UserIntent {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        type: parsed.type || 'discovery',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
        categories: parsed.categories || [],
        features: parsed.features || [],
        constraints: parsed.constraints || {},
      };
    } catch (error) {
      this.logger.warn('Failed to parse intent response, using enhanced fallback', error.message);
      return this.getEnhancedFallbackIntent('', {} as ConversationContext);
    }
  }

  private parseFollowUpResponse(response: string): string[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed.questions || [];
    } catch (error) {
      this.logger.warn('Failed to parse follow-up response, using enhanced fallback', error.message);
      return [];
    }
  }

  private parseTransitionResponse(response: string): { shouldTransition: boolean; reason: string } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);
      return {
        shouldTransition: parsed.shouldTransition || false,
        reason: parsed.reason || 'Automatic evaluation',
      };
    } catch (error) {
      this.logger.warn('Failed to parse transition response, using enhanced fallback', error.message);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  private formatEntitiesForChat(entities: CandidateEntity[]): string {
    return entities.slice(0, 5).map((entity, index) => {
      const categories = entity.categories?.map(c => c.category.name).join(', ') || 'General';
      const features = entity.features?.map(f => f.feature.name).join(', ') || 'Various features';

      return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - ${entity.shortDescription || entity.description || 'AI tool'}
   - Categories: ${categories}
   - Key Features: ${features}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5` : 'Not rated'}
   - 🎯 Use EXACT ID: ${entity.id}`;
    }).join('\n\n');
  }

  private formatUserProfile(context: ConversationContext): string {
    const prefs = context.userPreferences || {};
    return `- Technical Level: ${prefs.technical_level || 'Not specified'}
- Budget Preference: ${prefs.budget || 'Not specified'}
- Preferred Categories: ${prefs.preferred_categories?.join(', ') || 'None specified'}
- Conversation Stage: ${context.conversationStage}
- Tools Discovered: ${context.discoveredEntities?.length || 0}
- Engagement Level: ${(context.metadata as any)?.userEngagementLevel || 'Not assessed'}`;
  }

  // Enhanced fallback methods
  private getEnhancedFallbackRecommendation(candidateEntities: CandidateEntity[]): LlmRecommendation {
    const sortedEntities = candidateEntities
      .sort((a, b) => (b.avgRating || 0) - (a.avgRating || 0))
      .slice(0, 3);

    return {
      recommendedEntityIds: sortedEntities.map((e) => e.id),
      explanation: `Based on the available options and user ratings, I've selected the top-performing AI tools that should help with your needs. Each of these tools has proven effectiveness and positive user feedback.`,
    };
  }

  private getEnhancedFallbackChatResponse(userMessage: string, context: ConversationContext): ChatResponse {
    const message = this.getContextAwareFallbackMessage(context);

    return {
      message,
      intent: this.getEnhancedFallbackIntent(userMessage, context),
      followUpQuestions: this.getEnhancedFallbackFollowUpQuestions(context),
      shouldTransitionToRecommendations: false,
      conversationStage: context.conversationStage,
      metadata: { responseTime: 0, llmProvider: 'ANTHROPIC_ENHANCED_FALLBACK' },
    };
  }

  private getEnhancedFallbackIntent(userMessage: string, context: ConversationContext): UserIntent {
    // Simple keyword-based intent classification
    const messageLower = userMessage.toLowerCase();
    
    if (messageLower.includes('recommend') || messageLower.includes('suggest')) {
      return { type: 'refinement', confidence: 0.7, entities: [], categories: [], features: [], constraints: {} };
    }
    
    if (messageLower.includes('compare') || messageLower.includes('vs') || messageLower.includes('versus')) {
      return { type: 'comparison', confidence: 0.7, entities: [], categories: [], features: [], constraints: {} };
    }
    
    if (messageLower.includes('what is') || messageLower.includes('tell me about')) {
      return { type: 'specific_tool', confidence: 0.6, entities: [], categories: [], features: [], constraints: {} };
    }

    return { type: 'discovery', confidence: 0.5, entities: [], categories: [], features: [], constraints: {} };
  }

  private getEnhancedFallbackFollowUpQuestions(context: ConversationContext): string[] {
    const stage = context.conversationStage;
    const messageCount = context.messages?.length || 0;

    const questionsByStage = {
      greeting: [
        "What type of work or projects are you focusing on?",
        "Are you looking for AI tools for business or personal use?"
      ],
      discovery: [
        "What's your experience level with AI tools?",
        "Do you have a specific budget range in mind?",
        "What's the main challenge you're trying to solve?"
      ],
      refinement: [
        "Would you prefer free tools or are you open to paid solutions?",
        "How important is ease of use versus advanced features?",
        "Do you need tools that integrate with other software?"
      ],
      recommendation: [
        "Would you like to see some specific recommendations based on our discussion?",
        "Should I focus on the most popular options or newer innovative tools?"
      ],
      comparison: [
        "What criteria are most important for your decision?",
        "Would you like me to compare pricing or features?"
      ]
    };

    return questionsByStage[stage] || questionsByStage.discovery;
  }

  private getEnhancedTransitionDecision(context: ConversationContext): { shouldTransition: boolean; reason: string } {
    const messageCount = context.messages?.length || 0;
    const entitiesDiscovered = context.discoveredEntities?.length || 0;
    const hasPreferences = Object.keys(context.userPreferences || {}).length > 0;
    const topics = (context.metadata as any)?.conversationTopics || [];

    if (messageCount >= 6 && entitiesDiscovered >= 3 && hasPreferences && topics.length >= 2) {
      return {
        shouldTransition: true,
        reason: 'User has engaged meaningfully with sufficient context and entity discovery for quality recommendations',
      };
    }

    if (messageCount >= 8) {
      return {
        shouldTransition: true,
        reason: 'Conversation has sufficient depth to provide recommendations',
      };
    }

    return {
      shouldTransition: false,
      reason: 'Need more conversation context and user engagement before transitioning to recommendations',
    };
  }
}
